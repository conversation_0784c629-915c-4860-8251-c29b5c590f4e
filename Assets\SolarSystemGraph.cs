using System.Collections.Generic;
using UnityEngine;

public class SolarSystemGraph : MonoBehaviour
{
    // Singleton pattern
    public static SolarSystemGraph Instance;

    // CHANGE #4: Add a way to check if initialized
    public bool IsInitialized { get; private set; } = false;

    // Dictionary mapping GameObjects to their node IDs
    public Dictionary<GameObject, int> bodyToNodeId = new Dictionary<GameObject, int>();
    public Dictionary<int, GameObject> nodeIdToBody = new Dictionary<int, GameObject>();
    
    // Adjacency list representation of the graph
    public Dictionary<int, List<PathConnection>> adjacencyList = new Dictionary<int, List<PathConnection>>();
    
    private int nextNodeId = 0;

    // CHANGE #5: Add more debug logging to Awake
    private void Awake()
    {   
        if (Instance != null && Instance != this)
        {
            Debug.LogWarning("Multiple SolarSystemGraph instances detected! " +
                           "Existing on " + Instance.gameObject.name + 
                           ", new on " + gameObject.name);
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    // Structure to represent a connection between nodes
    public class PathConnection
    {
        public int DestinationNodeId;
        public float DeltaVCost;
        public GameObject PathVisual;
        
        public PathConnection(int destinationNodeId, float deltaVCost, GameObject pathVisual)
        {
            DestinationNodeId = destinationNodeId;
            DeltaVCost = deltaVCost;
            PathVisual = pathVisual;
        }
    }

    // Add a celestial body to the graph
    public int AddNode(GameObject celestialBody)
    {
        if (celestialBody == null)
        {
            Debug.LogError("Trying to add null celestial body to graph!");
            return -1;
        }
        
        // Check if already in graph
        if (bodyToNodeId.ContainsKey(celestialBody))
        {
            Debug.Log($"{celestialBody.name} already in graph with ID {bodyToNodeId[celestialBody]}");
            return bodyToNodeId[celestialBody];
        }
        
        int nodeId = nextNodeId++;
        bodyToNodeId[celestialBody] = nodeId;
        nodeIdToBody[nodeId] = celestialBody;
        adjacencyList[nodeId] = new List<PathConnection>();
        //Debug.Log($"Added {celestialBody.name} to graph with ID {nodeId}");
        
        IsInitialized = true;
        return nodeId;
    }

     // Add a ONE WAY path between two celestial bodies
    public void AddDirectionalPath(GameObject from, GameObject to, float deltaVCost, GameObject pathVisual)
    {
        // Make sure both bodies exist in the graph
        if (!bodyToNodeId.ContainsKey(from))
            AddNode(from);
        if (!bodyToNodeId.ContainsKey(to))
            AddNode(to);

        int fromNodeId = bodyToNodeId[from];
        int toNodeId = bodyToNodeId[to];

        // Add one-way connection only
        adjacencyList[fromNodeId].Add(new PathConnection(toNodeId, deltaVCost, pathVisual));
    }

    // Get all outgoing connections from a celestial body
    public List<PathConnection> GetConnections(GameObject celestialBody)
    {
        if (bodyToNodeId.ContainsKey(celestialBody))
        {
            int nodeId = bodyToNodeId[celestialBody];
            return adjacencyList[nodeId];
        }
        return new List<PathConnection>();
    }

    // Get celestial body from node ID
    public GameObject GetBody(int nodeId)
    {
        if (nodeIdToBody.ContainsKey(nodeId))
            return nodeIdToBody[nodeId];
        return null;
    }

    // Get directional delta-v cost between two celestial bodies
    public float GetDeltaVCost(GameObject from, GameObject to)
    {
        if (!bodyToNodeId.ContainsKey(from) || !bodyToNodeId.ContainsKey(to))
            return float.MaxValue;

        int fromNodeId = bodyToNodeId[from];
        int toNodeId = bodyToNodeId[to];

        foreach (var connection in adjacencyList[fromNodeId])
        {
            if (connection.DestinationNodeId == toNodeId)
                return connection.DeltaVCost;
        }

        return float.MaxValue; // No direct connection in this direction
    }

    // CHANGE #6: Add debug method
    public void LogGraphContents()
    {
        Debug.Log($"Graph contains {bodyToNodeId.Count} bodies:");
        /*
        foreach (var entry in bodyToNodeId)
        {
            Debug.Log($"- {entry.Key.name} (ID: {entry.Value})");
        }
        */
    }
}