using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Manages world cards and their interactions with the game
/// </summary>
public class WorldManager : MonoBehaviour
{
    // Singleton pattern
    public static WorldManager Instance { get; private set; }
    
    [Header("World Bodies")]
    [SerializeField] private List<GameObject> celestialBodies = new List<GameObject>();

    [Header("Orbit Locations")]
    [SerializeField] private List<GameObject> orbitLocations = new List<GameObject>();
    
    [Header("World Card Templates")]
    [SerializeField] private GameObject lowSolarOrbitCardTemplate;
    [SerializeField] private GameObject mercuryCardTemplate;
    [SerializeField] private GameObject venusCardTemplate;
    [SerializeField] private GameObject earthCardTemplate;
    [SerializeField] private GameObject marsCardTemplate;
    [SerializeField] private GameObject moonCardTemplate;
    [SerializeField] private GameObject callistoCardTemplate;
    [SerializeField] private GameObject ganymedeCardTemplate;
    [SerializeField] private GameObject europaCardTemplate;
    [SerializeField] private GameObject ioCardTemplate;
    [SerializeField] private GameObject jupiterCardTemplate;
    [SerializeField] private GameObject titanCardTemplate;
    [SerializeField] private GameObject enceladusCardTemplate;
    [SerializeField] private GameObject saturnCardTemplate;
    [SerializeField] private GameObject uranusCardTemplate;
    [SerializeField] private GameObject tritonCardTemplate;
    [SerializeField] private GameObject neptuneCardTemplate;

    [Header("Orbit Card Template")]
    [SerializeField] private GameObject lowOrbitAboveCardTemplate;
    
    // Cache of body name to GameObject
    private Dictionary<string, GameObject> bodyNameToObject = new Dictionary<string, GameObject>();

    // Cache of body to world data
    private Dictionary<GameObject, WorldData> bodyToWorldData = new Dictionary<GameObject, WorldData>();

    // Cache of orbit name to GameObject
    private Dictionary<string, GameObject> orbitNameToObject = new Dictionary<string, GameObject>();

    // Cache of orbit to world data
    private Dictionary<GameObject, OrbitData> orbitToWorldData = new Dictionary<GameObject, OrbitData>();

    // Cache of body name to card template
    private Dictionary<string, GameObject> bodyNameToCardTemplate = new Dictionary<string, GameObject>();
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        
        // Initialize body caches
        InitializeCelestialBodies();

        // Initialize orbit locations
        InitializeOrbitLocations();

        // Initialize card template dictionary
        InitializeCardTemplates();
    }
    
    private void Start()
    {
        // Validate world data
        //ValidateWorldData();
    }
    
    /// <summary>
    /// Initialize card template dictionary
    /// </summary>
    private void InitializeCardTemplates()
    {
        // Map celestial body names to their card templates
        // Special case for Low Solar Orbit - has its own unique design
        AddCardTemplate("LowSolarOrbit", lowSolarOrbitCardTemplate);

        // Regular planet templates
        AddCardTemplate("Mercury", mercuryCardTemplate);
        AddCardTemplate("Venus", venusCardTemplate);
        AddCardTemplate("Earth", earthCardTemplate);
        AddCardTemplate("Mars", marsCardTemplate);
        AddCardTemplate("Moon", moonCardTemplate);
        AddCardTemplate("Callisto", callistoCardTemplate);
        AddCardTemplate("Ganymede", ganymedeCardTemplate);
        AddCardTemplate("Europa", europaCardTemplate);
        AddCardTemplate("Io", ioCardTemplate);
        AddCardTemplate("Jupiter", jupiterCardTemplate);
        AddCardTemplate("Titan", titanCardTemplate);
        AddCardTemplate("Enceladus", enceladusCardTemplate);
        AddCardTemplate("Saturn", saturnCardTemplate);
        AddCardTemplate("Uranus", uranusCardTemplate);
        AddCardTemplate("Triton", tritonCardTemplate);
        AddCardTemplate("Neptune", neptuneCardTemplate);
    }
    
    /// <summary>
    /// Helper to add a card template to the dictionary
    /// </summary>
    private void AddCardTemplate(string bodyName, GameObject template)
    {
        if (template != null)
        {
            bodyNameToCardTemplate[bodyName] = template;
        }
        else
        {
            Debug.LogWarning($"Card template for {bodyName} is null!");
        }
    }
    
    /// <summary>
    /// Initialize and cache celestial bodies
    /// </summary>
    private void InitializeCelestialBodies()
    {
        // If we already have celestial bodies assigned in inspector, use those
        if (celestialBodies != null && celestialBodies.Count > 0)
        {
            // Clear caches before repopulating
            bodyNameToObject.Clear();
            bodyToWorldData.Clear();
            
            // Process each body
            foreach (GameObject bodyObject in celestialBodies)
            {
                if (bodyObject == null) continue;
                
                PlanetBody planetBody = bodyObject.GetComponent<PlanetBody>();
                if (planetBody == null)
                {
                    Debug.LogWarning($"GameObject {bodyObject.name} doesn't have a PlanetBody component!");
                    continue;
                }
                
                // Cache by name for quick lookup (use PlanetBody.Name if available, otherwise GameObject name)
                string bodyName = string.IsNullOrEmpty(planetBody.Name) ? bodyObject.name : planetBody.Name;
                bodyNameToObject[bodyName] = bodyObject;
                
                // Create world data entry
                WorldData worldData = new WorldData
                {
                    CelestialBody = bodyObject,
                    PlanetBody = planetBody,
                    Name = bodyName,
                    Type = planetBody.Type,
                    IsExplored = false,
                    OccupyingPlayer = null,
                    PlayersPresent = new List<Player>()
                };
                
                bodyToWorldData[bodyObject] = worldData;
            }
            
            return;
        }
        
        // If no bodies assigned, find all PlanetBody components
        PlanetBody[] allBodies = FindObjectsByType<PlanetBody>(FindObjectsSortMode.None);
        celestialBodies.Clear();
        
        foreach (PlanetBody body in allBodies)
        {
            GameObject bodyObject = body.gameObject;
            celestialBodies.Add(bodyObject);
            
            // Cache by name for quick lookup
            bodyNameToObject[body.Name] = bodyObject;
            
            // Create world data entry
            WorldData worldData = new WorldData
            {
                CelestialBody = bodyObject,
                PlanetBody = body,
                Name = body.Name,
                Type = body.Type,
                IsExplored = false,
                OccupyingPlayer = null,
                PlayersPresent = new List<Player>()
            };
            
            bodyToWorldData[bodyObject] = worldData;
            
            Debug.Log($"Found and cached celestial body: {body.Name} ({body.Type})");
        }
    }

    /// <summary>
    /// Initialize and cache orbit locations (finds existing orbit objects in scene by name pattern: "Low[Planet]Orbit")
    /// </summary>
    private void InitializeOrbitLocations()
    {
        // Clear caches before repopulating
        orbitNameToObject.Clear();
        orbitToWorldData.Clear();
        orbitLocations.Clear();

        // Find all existing orbit objects in the scene by name pattern (e.g., "LowMercuryOrbit")
        GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);

        foreach (GameObject obj in allObjects)
        {
            // Check if this looks like an orbit location (exact pattern: "Low[Planet]Orbit")
            if (IsValidOrbitLocation(obj))
            {
                // Add OrbitLocation component if it doesn't exist
                if (!obj.TryGetComponent<OrbitLocation>(out OrbitLocation orbitComponent))
                {
                    orbitComponent = obj.AddComponent<OrbitLocation>();

                    // Special case: Low Solar Orbit doesn't have a parent celestial body
                    if (obj.name == "LowSolarOrbit")
                    {
                        orbitComponent.Initialize(null); // No parent body for solar orbit
                    }
                    else
                    {
                        // Try to find the parent celestial body by name matching (e.g., "LowMercuryOrbit" -> "Mercury")
                        GameObject parentBody = FindParentCelestialBodyForOrbit(obj);
                        if (parentBody != null)
                        {
                            orbitComponent.Initialize(parentBody);
                        }
                        else
                        {
                            Debug.LogWarning($"Could not find parent celestial body for orbit: {obj.name}");
                        }
                    }
                }

                // Add to our tracking
                orbitLocations.Add(obj);
                orbitNameToObject[obj.name] = obj;

                // Create orbit data entry
                OrbitData orbitData = new OrbitData
                {
                    OrbitLocation = obj,
                    OrbitComponent = orbitComponent,
                    Name = obj.name,
                    ParentCelestialBody = orbitComponent.ParentCelestialBody,
                    IsExplored = false,
                    OccupyingPlayer = null,
                    PlayersPresent = new List<Player>()
                };

                orbitToWorldData[obj] = orbitData;
            }
        }

        Debug.Log($"Initialized {orbitLocations.Count} existing orbit locations");
    }

    /// <summary>
    /// Check if a GameObject is a valid orbit location based on naming convention
    /// Must match exact pattern: "Low[Planet]Orbit" (e.g., "LowMercuryOrbit")
    /// </summary>
    private bool IsValidOrbitLocation(GameObject obj)
    {
        if (obj == null) return false;

        string name = obj.name;

        // Must match exact pattern: "Low[Something]Orbit"
        return name.StartsWith("Low", System.StringComparison.Ordinal) &&
               name.EndsWith("Orbit", System.StringComparison.Ordinal) &&
               name.Length > 8; // "Low" + "Orbit" = 8 chars, need something in between
    }

    /// <summary>
    /// Try to find the parent celestial body for an orbit by name matching
    /// Handles names like "LowMercuryOrbit" -> "Mercury"
    /// </summary>
    private GameObject FindParentCelestialBodyForOrbit(GameObject orbitObject)
    {
        string orbitName = orbitObject.name;

        // Extract planet name from orbit name
        // "LowMercuryOrbit" -> "Mercury"
        string planetName = ExtractPlanetNameFromOrbit(orbitName);

        if (string.IsNullOrEmpty(planetName)) return null;

        // Try to match extracted planet name with celestial body name
        foreach (GameObject celestialBody in celestialBodies)
        {
            if (string.Equals(celestialBody.name, planetName, System.StringComparison.OrdinalIgnoreCase))
            {
                return celestialBody;
            }
        }

        // If no match found, return null
        return null;
    }

    /// <summary>
    /// Extract planet name from orbit name
    /// "LowMercuryOrbit" -> "Mercury"
    /// "LowEarthOrbit" -> "Earth"
    /// "LowSolarOrbit" -> null (special case - no parent celestial body)
    /// </summary>
    private string ExtractPlanetNameFromOrbit(string orbitName)
    {
        if (string.IsNullOrEmpty(orbitName)) return null;

        // Special case: Low Solar Orbit doesn't have a parent celestial body
        if (orbitName == "LowSolarOrbit")
        {
            return null;
        }

        // Remove "Low" prefix and "Orbit" suffix
        string result = orbitName;

        if (result.StartsWith("Low", System.StringComparison.OrdinalIgnoreCase))
        {
            result = result.Substring(3); // Remove "Low"
        }

        if (result.EndsWith("Orbit", System.StringComparison.OrdinalIgnoreCase))
        {
            result = result.Substring(0, result.Length - 5); // Remove "Orbit"
        }

        return result;
    }
    
    /// <summary>
    /// Validate world data against the world data in the game files
    /// </summary>
    private void ValidateWorldData()
    {
        // This would compare the bodies in the scene with data from text files
        // For example, ensure Mercury has the right deposits, etc.
    }
    
    /// <summary>
    /// Get a celestial body by name
    /// </summary>
    public GameObject GetCelestialBodyByName(string name)
    {
        // First check if the body is in our cache
        if (bodyNameToObject.ContainsKey(name))
        {
            GameObject body = bodyNameToObject[name];
            
            return body;
        }
            
        // If not found, try looking up GameObject by name (fallback)
        GameObject bodyObject = GameObject.Find(name);
        if (bodyObject != null)
        {   
            // Cache it for future lookups
            PlanetBody planetBody = bodyObject.GetComponent<PlanetBody>();
            if (planetBody != null)
            {
                bodyNameToObject[name] = bodyObject;
                
                // Add to world data if not already there
                if (!bodyToWorldData.ContainsKey(bodyObject))
                {
                    WorldData worldData = new WorldData
                    {
                        CelestialBody = bodyObject,
                        PlanetBody = planetBody,
                        Name = name,
                        Type = planetBody.Type,
                        IsExplored = false,
                        OccupyingPlayer = null,
                        PlayersPresent = new List<Player>()
                    };
                    
                    bodyToWorldData[bodyObject] = worldData;
                }

                return bodyObject;
            }
        }
            
        Debug.LogWarning($"Celestial body not found: {name}");
        return null;
    }

    /// <summary>
    /// Get an orbit location by name
    /// </summary>
    public GameObject GetOrbitLocationByName(string name)
    {
        // First check if the orbit is in our cache
        if (orbitNameToObject.ContainsKey(name))
        {
            GameObject orbit = orbitNameToObject[name];
            return orbit;
        }

        // If not found, try looking up GameObject by name (fallback)
        GameObject orbitObject = GameObject.Find(name);
        if (orbitObject != null)
        {
            // Cache it for future lookups
            OrbitLocation orbitLocation = orbitObject.GetComponent<OrbitLocation>();
            if (orbitLocation != null)
            {
                orbitNameToObject[name] = orbitObject;

                // Add to orbit data if not already there
                if (!orbitToWorldData.ContainsKey(orbitObject))
                {
                    OrbitData orbitData = new OrbitData
                    {
                        OrbitLocation = orbitObject,
                        OrbitComponent = orbitLocation,
                        Name = name,
                        ParentCelestialBody = orbitLocation.ParentCelestialBody,
                        IsExplored = false,
                        OccupyingPlayer = null,
                        PlayersPresent = new List<Player>()
                    };

                    orbitToWorldData[orbitObject] = orbitData;
                }

                return orbitObject;
            }
        }

        Debug.LogWarning($"Orbit location not found: {name}");
        return null;
    }
    
    /// <summary>
    /// Get world data for a celestial body
    /// </summary>
    public WorldData GetWorldData(GameObject body)
    {
        if (bodyToWorldData.ContainsKey(body))
            return bodyToWorldData[body];

        Debug.LogWarning($"World data not found for body: {body.name}");
        return null;
    }

    /// <summary>
    /// Get orbit data for an orbit location
    /// </summary>
    public OrbitData GetOrbitData(GameObject orbit)
    {
        if (orbitToWorldData.ContainsKey(orbit))
            return orbitToWorldData[orbit];

        Debug.LogWarning($"Orbit data not found for orbit: {orbit.name}");
        return null;
    }

    /// <summary>
    /// Check if a GameObject is an orbit location
    /// </summary>
    public bool IsOrbitLocation(GameObject location)
    {
        return location.GetComponent<OrbitLocation>() != null;
    }

    /// <summary>
    /// Check if a GameObject is a celestial body
    /// </summary>
    public bool IsCelestialBody(GameObject location)
    {
        return location.GetComponent<PlanetBody>() != null;
    }

    /// <summary>
    /// Create a world card instance for a player's play area (supports both celestial bodies and orbit locations)
    /// </summary>
    public GameObject CreateWorldCard(GameObject location, Transform parent)
    {
        if (location == null)
        {
            Debug.LogError("Cannot create world card for null location!");
            return null;
        }

        string locationName = location.name;
        GameObject cardTemplate = null;

        // Check if this is an orbit location
        OrbitLocation orbitLocation = location.GetComponent<OrbitLocation>();
        if (orbitLocation != null)
        {
            locationName = orbitLocation.Name;

            // Special case: Low Solar Orbit has its own unique card design
            if (locationName == "LowSolarOrbit" && bodyNameToCardTemplate.ContainsKey("LowSolarOrbit"))
            {
                cardTemplate = bodyNameToCardTemplate["LowSolarOrbit"];
            }
            // For other orbit locations, use the generic "Low Orbit Above" card template
            else if (lowOrbitAboveCardTemplate != null)
            {
                cardTemplate = lowOrbitAboveCardTemplate;
            }
            else
            {
                // Fallback to parent celestial body's card template if orbit template is not set
                if (orbitLocation.ParentCelestialBody != null)
                {
                    PlanetBody parentPlanet = orbitLocation.ParentCelestialBody.GetComponent<PlanetBody>();
                    if (parentPlanet != null)
                    {
                        string parentName = parentPlanet.Name;
                        if (bodyNameToCardTemplate.ContainsKey(parentName))
                        {
                            cardTemplate = bodyNameToCardTemplate[parentName];
                        }
                    }
                }
                Debug.LogWarning("Low Orbit Above card template is not assigned! Using parent body template as fallback.");
            }
        }
        else
        {
            // Check if this is a celestial body
            PlanetBody planetBody = location.GetComponent<PlanetBody>();
            if (planetBody != null && !string.IsNullOrEmpty(planetBody.Name))
            {
                locationName = planetBody.Name;
            }

            // Get the card template for this body
            if (bodyNameToCardTemplate.ContainsKey(locationName))
            {
                cardTemplate = bodyNameToCardTemplate[locationName];
            }
            // Special handling for orbit locations that might not have OrbitLocation component yet
            else if (locationName == "LowSolarOrbit" && bodyNameToCardTemplate.ContainsKey("LowSolarOrbit"))
            {
                cardTemplate = bodyNameToCardTemplate["LowSolarOrbit"];
            }
        }

        if (cardTemplate == null)
        {
            Debug.LogError($"No card template found for {locationName}!");
            return null;
        }

        // Create card instance from template
        GameObject card = Instantiate(cardTemplate, parent);

        // Set name
        card.name = $"WorldCard_{locationName}";

        // Get or add the visual component
        WorldCardVisual visual = card.GetComponent<WorldCardVisual>();
        if (visual == null)
        {
            visual = card.AddComponent<WorldCardVisual>();
            Debug.Log($"Added WorldCardVisual component to {locationName} card");
        }

        // Setup the visual component
        visual.SetupCard(location);

        // Add collider if missing (for click detection)
        if (card.GetComponent<Collider>() == null)
        {
            BoxCollider boxCollider = card.AddComponent<BoxCollider>();
            // Adjust collider size based on card dimensions
            boxCollider.size = new Vector3(1f, 0.1f, 1.4f);
            Debug.Log($"Added BoxCollider to {locationName} card for click detection");
        }

        return card;
    }


    
    /// <summary>
    /// Get all celestial bodies
    /// </summary>
    public List<GameObject> GetAllCelestialBodies()
    {
        return new List<GameObject>(celestialBodies);
    }
    
    /// <summary>
    /// Mark a world as explored by a player
    /// </summary>
    public void MarkWorldExplored(GameObject body, Player player)
    {
        if (bodyToWorldData.ContainsKey(body))
        {
            WorldData data = bodyToWorldData[body];
            data.IsExplored = true;
            
            // Update the actual planet body
            PlanetBody planetBody = body.GetComponent<PlanetBody>();
            if (planetBody != null)
            {
                planetBody.MarkAsSurveyed(player);
            }
            
            // Update player's play area
            PlayAreaManager areaManager = PlayAreaManager.Instance;
            if (areaManager != null)
            {
                PlayerPlayArea playArea = areaManager.GetPlayerPlayArea(player.PlayerId);
                if (playArea != null)
                {
                    // Add the world card if player doesn't have it yet
                    playArea.AddWorldCard(body);
                }
            }
            
            Debug.Log($"Player {player.PlayerId} explored {data.Name}");
        }
    }
    
    /// <summary>
    /// Register a player as present at a world
    /// </summary>
    public void RegisterPlayerAtWorld(GameObject body, Player player)
    {
        if (bodyToWorldData.ContainsKey(body))
        {
            WorldData data = bodyToWorldData[body];
            
            if (!data.PlayersPresent.Contains(player))
            {
                data.PlayersPresent.Add(player);
                Debug.Log($"Player {player.PlayerId} is now present at {data.Name}");
            }
        }
    }
    
    /// <summary>
    /// Unregister a player as present at a world
    /// </summary>
    public void UnregisterPlayerAtWorld(GameObject body, Player player)
    {
        if (bodyToWorldData.ContainsKey(body))
        {
            WorldData data = bodyToWorldData[body];
            
            if (data.PlayersPresent.Contains(player))
            {
                data.PlayersPresent.Remove(player);
                Debug.Log($"Player {player.PlayerId} is no longer present at {data.Name}");
                
                // Check if player was occupying
                if (data.OccupyingPlayer == player)
                {
                    UpdateMilitaryOccupation(body);
                }
            }
        }
    }
    
    /// <summary>
    /// Update military occupation of a world
    /// </summary>
    public void UpdateMilitaryOccupation(GameObject body)
    {
        PlanetBody planetBody = body.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            planetBody.UpdateMilitaryOccupation();
            
            // Update our cached data
            if (bodyToWorldData.ContainsKey(body))
            {
                WorldData data = bodyToWorldData[body];
                data.OccupyingPlayer = planetBody.OccupyingPlayer;
            }
        }
    }

    /// <summary>
    /// Mark an orbit as explored by a player
    /// </summary>
    public void MarkOrbitExplored(GameObject orbit, Player player)
    {
        if (orbitToWorldData.ContainsKey(orbit))
        {
            OrbitData data = orbitToWorldData[orbit];
            data.IsExplored = true;

            // Update the actual orbit location
            OrbitLocation orbitLocation = orbit.GetComponent<OrbitLocation>();
            if (orbitLocation != null)
            {
                orbitLocation.MarkAsExplored(player);
            }

            // Update player's play area
            PlayAreaManager areaManager = PlayAreaManager.Instance;
            if (areaManager != null)
            {
                PlayerPlayArea playArea = areaManager.GetPlayerPlayArea(player.PlayerId);
                if (playArea != null)
                {
                    // Add the orbit world card if player doesn't have it yet
                    playArea.AddWorldCard(orbit);
                }
            }

            Debug.Log($"Player {player.PlayerId} explored {data.Name}");
        }
    }

    /// <summary>
    /// Get all orbit locations
    /// </summary>
    public List<GameObject> GetAllOrbitLocations()
    {
        return new List<GameObject>(orbitLocations);
    }

    /// <summary>
    /// Get orbit location for a specific celestial body
    /// </summary>
    public GameObject GetOrbitForCelestialBody(GameObject celestialBody)
    {
        foreach (GameObject orbit in orbitLocations)
        {
            OrbitLocation orbitLocation = orbit.GetComponent<OrbitLocation>();
            if (orbitLocation != null && orbitLocation.ParentCelestialBody == celestialBody)
            {
                return orbit;
            }
        }

        return null;
    }

    /// <summary>
    /// Refresh orbit locations (useful after finding existing orbits)
    /// </summary>
    public void RefreshOrbitLocations()
    {
        InitializeOrbitLocations();
        Debug.Log("Refreshed orbit locations");
    }
}

/// <summary>
/// Data structure for world-specific data
/// </summary>
[System.Serializable]
public class WorldData
{
    public GameObject CelestialBody;
    public PlanetBody PlanetBody;
    public string Name;
    public PlanetBody.BodyType Type;
    public bool IsExplored;
    public Player OccupyingPlayer;
    public List<Player> PlayersPresent;
}

/// <summary>
/// Data structure for orbit-specific data
/// </summary>
[System.Serializable]
public class OrbitData
{
    public GameObject OrbitLocation;
    public OrbitLocation OrbitComponent;
    public string Name;
    public GameObject ParentCelestialBody;
    public bool IsExplored;
    public Player OccupyingPlayer;
    public List<Player> PlayersPresent;
}