using UnityEngine;

public class CardActionChecker : MonoBehaviour
{
    private CardData cardData;
    private CardGlowEffect glowEffect;
    
    private void Awake()
    {
        glowEffect = GetComponent<CardGlowEffect>();
        if (glowEffect == null)
        {
            glowEffect = gameObject.AddComponent<CardGlowEffect>();
        }
    }
    
    public void Initialize(CardData data)
    {
        cardData = data;
        UpdateGlowState();
    }
    
    public void UpdateGlowState()
    {
        if (cardData == null || glowEffect == null)
            return;
        
        bool canGlow = ShouldCardGlow();
        glowEffect.SetCanGlow(canGlow);
    }
    
    private bool ShouldCardGlow()
    {
        // Always glow in card row
        if (IsInCardRow())
            return true;
        
        // Check if it's in a player area and has available actions
        if (IsInPlayerArea())
        {
            // Don't glow for these types
            if (IsUpgrade() || IsHabitationModule() || IsNonActivatableWonder())
            {
                return false;
            }
            
            // Check if the card has an activation ability
            return HasActivationAbility();
        }
        
        return false;
    }
    
    private bool IsInCardRow()
    {
        // Check if the card is a child of the card row parent
        Transform parent = transform.parent;
        while (parent != null)
        {
            if (parent.GetComponent<TechnologyDeckManager>() != null || parent.name.Contains("CardRow"))
            {
                return true;
            }
            parent = parent.parent;
        }
        return false;
    }
    
    private bool IsInPlayerArea()
    {
        Transform parent = transform.parent;
        while (parent != null)
        {
            if (parent.GetComponent<PlayerPlayArea>() != null)
            {
                return true;
            }
            parent = parent.parent;
        }
        return false;
    }
    
    private bool IsUpgrade()
    {
        return cardData.SubType != null && cardData.SubType.ToLower().Contains("upgrade");
    }
    
    private bool IsHabitationModule()
    {
        if (cardData.SubType != null && cardData.SubType.ToLower().Contains("habitation"))
            return true;
            
        if (cardData.Effect != null && cardData.Effect.ToLower().Contains("passive gain"))
            return true;
            
        return false;
    }
    
    private bool IsNonActivatableWonder()
    {
        if (!cardData.IsWonder)
            return false;
        
        // Check if the wonder has an activation in its effect text
        string effect = cardData.Effect.ToLower();
        return !effect.Contains("activate") && !effect.Contains("action");
    }

    /// <summary>
    /// Check if the card should glow (public accessor for UI system)
    /// </summary>
    public bool ShouldGlow()
    {
        return ShouldCardGlow();
    }


    private bool HasActivationAbility()
    {
        string effect = cardData.Effect.ToLower();

        // Check for activation keywords
        if (effect.Contains("activate") || effect.Contains("action"))
            return true;

        // Check for specific card types that can be activated
        string subType = cardData.SubType != null ? cardData.SubType.ToLower() : "";

        if (subType.Contains("extractor") || subType.Contains("processor"))
            return true;

        // Ships can move (which counts as an action)
        if (subType.Contains("ship"))
            return true;

        // Power modules can be built (they provide power when built)
        if (subType.Contains("power") && IsInCardRow())
            return true;

        // Buildings that can be built
        if ((subType.Contains("module") || subType.Contains("facility")) &&
            IsInCardRow()) // Only if in card row, not in play area
            return true;

        return false;
    }
}