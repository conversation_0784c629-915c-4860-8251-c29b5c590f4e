using UnityEngine;
using UnityEngine.EventSystems;

public class BuildModeGlowEffect : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
{
    [Header("Build Mode Glow Settings")]
    [SerializeField] private float buildModeGlowIntensity = 2.0f;
    [SerializeField] private float buildModeHoverIntensity = 3.0f;
    [SerializeField] private float transitionSpeed = 5f;

    
    private Material materialInstance;
    private Material originalMaterial;
    private Renderer cardRenderer;
    private Color originalColor;
    private Color targetColor;
    private bool isHovering = false;
    private bool isBuildModeActive = false;
    
    private void Awake()
    {
        cardRenderer = GetComponent<Renderer>();
        if (cardRenderer == null)
        {
            cardRenderer = GetComponent<MeshRenderer>();
        }

        if (cardRenderer != null && cardRenderer.material != null)
        {
            // Store the original shared material (before any component creates instances)
            originalMaterial = cardRenderer.sharedMaterial;

            // Always create our own material instance for build mode effects
            // This way we don't interfere with CardGlowEffect
            materialInstance = new Material(originalMaterial);
            originalColor = originalMaterial.color;
            targetColor = originalColor;
        }
    }

    private System.Collections.IEnumerator PulseGlow()
    {
        while (isBuildModeActive)
        {
            // Don't pulse if hovering - let hover take control
            if (!isHovering)
            {
                // Pulse between base glow and slightly brighter
                Color glowColor = new Color(1f, 0.8f, 0.2f, 1f); // Bright yellow-orange
                Color baseGlow = Color.Lerp(originalColor, glowColor, 0.5f) * buildModeGlowIntensity;
                Color brightGlow = Color.Lerp(originalColor, glowColor, 0.8f) * buildModeGlowIntensity;

                // Fade up
                for (float t = 0; t < 1; t += Time.deltaTime * 1.5f)
                {
                    if (!isBuildModeActive || isHovering) yield break;
                    targetColor = Color.Lerp(baseGlow, brightGlow, t);
                    yield return null;
                }

                // Fade down
                for (float t = 0; t < 1; t += Time.deltaTime * 1.5f)
                {
                    if (!isBuildModeActive || isHovering) yield break;
                    targetColor = Color.Lerp(brightGlow, baseGlow, t);
                    yield return null;
                }
            }
            else
            {
                yield return null;
            }
        }
    }

    public void SetBuildModeGlow(bool active)
    {
        isBuildModeActive = active;

        if (isBuildModeActive)
        {
            // Switch to our build mode material
            if (cardRenderer != null && materialInstance != null)
            {
                cardRenderer.material = materialInstance;
            }

            // Start the pulsing animation - use a bright yellow/orange glow
            Color glowColor = new Color(1f, 0.8f, 0.2f, 1f); // Bright yellow-orange
            targetColor = Color.Lerp(originalColor, glowColor, 0.7f) * buildModeGlowIntensity;
            StartCoroutine(PulseGlow());
        }
        else
        {
            isHovering = false;
            StopAllCoroutines();

            // Check if CardGlowEffect exists and let it handle material restoration
            if (TryGetComponent(out CardGlowEffect cardGlow))
            {
                // Force CardGlowEffect to recreate its material instance with the original material
                cardGlow.RefreshMaterial();
            }
            else if (cardRenderer != null && originalMaterial != null)
            {
                // No CardGlowEffect, restore original material directly
                cardRenderer.material = originalMaterial;
            }
        }
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        if (!isBuildModeActive) return;

        isHovering = true;
        StopAllCoroutines(); // Stop pulsing when hovering
        Color glowColor = new Color(1f, 0.8f, 0.2f, 1f); // Bright yellow-orange
        targetColor = Color.Lerp(originalColor, glowColor, 0.9f) * buildModeHoverIntensity;
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (!isBuildModeActive) return;

        isHovering = false;
        StartCoroutine(PulseGlow()); // Resume pulsing when not hovering
    }
    
    private void Update()
    {
        if (materialInstance != null)
        {
            materialInstance.color = Color.Lerp(materialInstance.color, targetColor, Time.deltaTime * transitionSpeed);
        }
    }
    
    private void OnDestroy()
    {
        if (materialInstance != null)
        {
            Destroy(materialInstance);
        }
    }
}