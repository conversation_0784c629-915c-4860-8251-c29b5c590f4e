using UnityEngine;
using System.Collections;

/// <summary>
/// Initializes the orbit system and ensures all components work together
/// </summary>
public class OrbitSystemInitializer : MonoBehaviour
{
    [Header("Initialization Settings")]
    [SerializeField] private bool initializeOnStart = true;
    [SerializeField] private bool findExistingOrbits = true;
    [SerializeField] private float initializationDelay = 2f;
    
    [Header("Status")]
    [SerializeField] private bool isInitialized = false;
    
    private void Start()
    {
        if (initializeOnStart)
        {
            StartCoroutine(InitializeOrbitSystem());
        }
    }
    
    [ContextMenu("Initialize Orbit System")]
    public void InitializeOrbitSystemManual()
    {
        StartCoroutine(InitializeOrbitSystem());
    }
    
    private IEnumerator InitializeOrbitSystem()
    {
        Debug.Log("=== Initializing Orbit System ===");
        
        // Wait for other systems to initialize
        yield return new WaitForSeconds(initializationDelay);
        
        // Step 1: Ensure WorldManager is ready
        if (!EnsureWorldManagerReady())
        {
            Debug.LogError("WorldManager not ready! Orbit system initialization failed.");
            yield break;
        }
        
        // Step 2: Find and initialize existing orbit locations
        if (findExistingOrbits)
        {
            InitializeExistingOrbitLocations();
            yield return new WaitForSeconds(0.5f);
        }
        
        // Step 3: Ensure SolarSystemGraph is ready
        if (!EnsureSolarSystemGraphReady())
        {
            Debug.LogError("SolarSystemGraph not ready! Orbit system initialization failed.");
            yield break;
        }
        
        // Step 4: Initialize orbit locations in WorldManager
        InitializeWorldManagerOrbits();
        yield return new WaitForSeconds(0.5f);
        
        // Step 5: Orbit navigation paths already exist in delta-V map
        // No additional navigation setup needed
        yield return new WaitForSeconds(0.5f);
        
        // Step 6: Verify system integrity
        if (VerifySystemIntegrity())
        {
            isInitialized = true;
            Debug.Log("✓ Orbit System successfully initialized!");
            LogSystemStatus();
        }
        else
        {
            Debug.LogError("✗ Orbit System initialization failed verification!");
        }
    }
    
    private bool EnsureWorldManagerReady()
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager instance not found!");
            return false;
        }
        
        // Check if celestial bodies are loaded
        var celestialBodies = worldManager.GetAllCelestialBodies();
        if (celestialBodies.Count == 0)
        {
            Debug.LogWarning("No celestial bodies found in WorldManager!");
            return false;
        }
        
        Debug.Log($"WorldManager ready with {celestialBodies.Count} celestial bodies");
        return true;
    }
    
    private void InitializeExistingOrbitLocations()
    {
        Debug.Log("Finding and initializing existing orbit locations...");

        // Find or create orbit initializer
        OrbitLocationInitializer initializer = FindFirstObjectByType<OrbitLocationInitializer>();
        if (initializer == null)
        {
            GameObject initializerObj = new GameObject("OrbitLocationInitializer");
            initializer = initializerObj.AddComponent<OrbitLocationInitializer>();
            Debug.Log("Created OrbitLocationInitializer");
        }

        // Initialize existing orbits
        initializer.InitializeExistingOrbitLocations();
    }
    
    private bool EnsureSolarSystemGraphReady()
    {
        SolarSystemGraph graph = SolarSystemGraph.Instance;
        if (graph == null)
        {
            Debug.LogError("SolarSystemGraph instance not found!");
            return false;
        }
        
        if (!graph.IsInitialized)
        {
            Debug.LogWarning("SolarSystemGraph not yet initialized, waiting...");
            return false;
        }
        
        Debug.Log("SolarSystemGraph ready");
        return true;
    }
    
    private void InitializeWorldManagerOrbits()
    {
        Debug.Log("Initializing WorldManager orbit support...");
        
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager != null)
        {
            worldManager.RefreshOrbitLocations();
        }
    }
    
    // Orbit navigation is already handled by existing delta-V map
    // No additional setup needed
    
    private bool VerifySystemIntegrity()
    {
        Debug.Log("Verifying orbit system integrity...");
        
        bool allChecksPass = true;
        
        // Check WorldManager
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("Verification failed: WorldManager not found");
            allChecksPass = false;
        }
        else
        {
            var orbits = worldManager.GetAllOrbitLocations();
            if (orbits.Count == 0)
            {
                Debug.LogWarning("Verification warning: No orbit locations found");
            }
            else
            {
                Debug.Log($"✓ Found {orbits.Count} orbit locations");
            }
        }
        
        // Check SolarSystemGraph
        SolarSystemGraph graph = SolarSystemGraph.Instance;
        if (graph == null)
        {
            Debug.LogError("Verification failed: SolarSystemGraph not found");
            allChecksPass = false;
        }
        else
        {
            Debug.Log("✓ SolarSystemGraph ready");
        }
        
        // Check orbit components
        OrbitLocation[] allOrbits = FindObjectsByType<OrbitLocation>(FindObjectsSortMode.None);
        int validOrbits = 0;
        foreach (OrbitLocation orbit in allOrbits)
        {
            if (orbit.ParentCelestialBody != null)
            {
                validOrbits++;
            }
        }
        
        if (validOrbits > 0)
        {
            Debug.Log($"✓ Found {validOrbits} valid orbit locations");
        }
        else
        {
            Debug.LogWarning("Verification warning: No valid orbit locations found");
        }
        
        return allChecksPass;
    }
    
    private void LogSystemStatus()
    {
        Debug.Log("=== Orbit System Status ===");
        
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager != null)
        {
            var celestialBodies = worldManager.GetAllCelestialBodies();
            var orbitLocations = worldManager.GetAllOrbitLocations();
            
            Debug.Log($"Celestial Bodies: {celestialBodies.Count}");
            Debug.Log($"Orbit Locations: {orbitLocations.Count}");
            
            // List orbit locations
            foreach (GameObject orbit in orbitLocations)
            {
                OrbitLocation orbitComponent = orbit.GetComponent<OrbitLocation>();
                if (orbitComponent != null)
                {
                    string parentName = orbitComponent.ParentCelestialBody != null ? 
                        orbitComponent.ParentCelestialBody.name : "Unknown";
                    Debug.Log($"  - {orbitComponent.Name} (Parent: {parentName})");
                }
            }
        }
        
        SolarSystemGraph graph = SolarSystemGraph.Instance;
        if (graph != null)
        {
            graph.LogGraphContents();
        }
        
        Debug.Log("=== End Status ===");
    }
    
    [ContextMenu("Force Reinitialize")]
    public void ForceReinitialize()
    {
        isInitialized = false;
        StartCoroutine(InitializeOrbitSystem());
    }
    
    [ContextMenu("Verify System")]
    public void VerifySystemManual()
    {
        bool isValid = VerifySystemIntegrity();
        if (isValid)
        {
            Debug.Log("✓ System verification passed!");
            LogSystemStatus();
        }
        else
        {
            Debug.LogError("✗ System verification failed!");
        }
    }
    
    public bool IsSystemReady()
    {
        return isInitialized;
    }
}
