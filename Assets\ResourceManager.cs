using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Manages resource-related functionality in the game
/// </summary>
public class ResourceManager : MonoBehaviour
{
    // Singleton pattern
    public static ResourceManager Instance { get; private set; }
    
    [Header("Resource Icons")]
    [SerializeField] private Sprite oreIcon;
    [SerializeField] private Sprite iceIcon;
    [SerializeField] private Sprite carbonIcon;
    [SerializeField] private Sprite siliconIcon;
    [SerializeField] private Sprite rareEarthsIcon;
    [SerializeField] private Sprite alloyIcon;
    [SerializeField] private Sprite fuelIcon;
    [SerializeField] private Sprite grapheneIcon;
    [SerializeField] private Sprite ceramicsIcon;
    [SerializeField] private Sprite microchipsIcon;
    [SerializeField] private Sprite superconductorsIcon;
    [SerializeField] private Sprite metallicHydrogenIcon;
    [SerializeField] private Sprite antimatterIcon;
    [SerializeField] private Sprite helium3Icon;
    [SerializeField] private Sprite powerIcon;
    [SerializeField] private Sprite vpIcon;
    [SerializeField] private Sprite scienceIcon;
    [SerializeField] private Sprite dollarsIcon;
    [SerializeField] private Sprite strengthIcon;
    
    // Cache of resource icons
    private Dictionary<ResourceType, Sprite> resourceIcons = new Dictionary<ResourceType, Sprite>();
    
    // Cache of resource colors
    private Dictionary<ResourceType, Color> resourceColors = new Dictionary<ResourceType, Color>();
    
    // Market prices (dynamic, changes with supply and demand)
    private Dictionary<ResourceType, int> marketPrices = new Dictionary<ResourceType, int>();
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        
        // Initialize resource icons
        InitializeResourceIcons();
        
        // Initialize resource colors
        InitializeResourceColors();
        
        // Initialize market prices
        InitializeMarketPrices();
    }

    /// <summary>
    /// Initialize resource icons
    /// </summary>
    private void InitializeResourceIcons()
    {
        // Assign icons to dictionary
        resourceIcons[ResourceType.Ore] = oreIcon;
        resourceIcons[ResourceType.Ice] = iceIcon;
        resourceIcons[ResourceType.Carbon] = carbonIcon;
        resourceIcons[ResourceType.Silicon] = siliconIcon;
        resourceIcons[ResourceType.RareEarths] = rareEarthsIcon;
        resourceIcons[ResourceType.Alloys] = alloyIcon;
        resourceIcons[ResourceType.Fuel] = fuelIcon;
        resourceIcons[ResourceType.Graphene] = grapheneIcon;
        resourceIcons[ResourceType.Ceramics] = ceramicsIcon;
        resourceIcons[ResourceType.Microchips] = microchipsIcon;
        resourceIcons[ResourceType.Superconductors] = superconductorsIcon;
        resourceIcons[ResourceType.MetallicHydrogen] = metallicHydrogenIcon;
        resourceIcons[ResourceType.Antimatter] = antimatterIcon;
        resourceIcons[ResourceType.Helium3] = helium3Icon;
        resourceIcons[ResourceType.Power] = powerIcon;
        resourceIcons[ResourceType.VP] = vpIcon;
        resourceIcons[ResourceType.Dollars] = dollarsIcon;
        resourceIcons[ResourceType.Science] = scienceIcon;
        resourceIcons[ResourceType.Strength] = strengthIcon; // No icon for strength
        
        // Check for missing icons and log warnings
        foreach (ResourceType type in System.Enum.GetValues(typeof(ResourceType)))
        {
            if (!resourceIcons.ContainsKey(type) || resourceIcons[type] == null)
            {
                Debug.LogWarning($"Missing icon for resource type: {type}");
            }
        }
    }
    
    /// <summary>
    /// Initialize resource colors
    /// </summary>
    private void InitializeResourceColors()
    {
        // Basic resources
        resourceColors[ResourceType.Ore] = new Color(0.7f, 0.7f, 0.7f); // Grey
        resourceColors[ResourceType.Ice] = new Color(0.7f, 0.8f, 1.0f); // Light blue
        resourceColors[ResourceType.Carbon] = new Color(0.2f, 0.6f, 0.2f); // Green
        resourceColors[ResourceType.Silicon] = new Color(0.8f, 0.2f, 0.2f); // Red
        resourceColors[ResourceType.RareEarths] = new Color(0.6f, 0.2f, 0.8f); // Purple
        
        // Advanced resources
        resourceColors[ResourceType.Alloys] = new Color(0.5f, 0.5f, 0.5f); // Darker grey
        resourceColors[ResourceType.Fuel] = new Color(0.0f, 0.5f, 1.0f); // Darker blue
        resourceColors[ResourceType.Graphene] = new Color(0.0f, 0.4f, 0.0f); // Darker green
        resourceColors[ResourceType.Ceramics] = new Color(0.6f, 0.1f, 0.1f); // Darker red
        resourceColors[ResourceType.Microchips] = new Color(0.4f, 0.0f, 0.6f); // Darker purple
        
        // Ultimate resources
        resourceColors[ResourceType.Superconductors] = new Color(1.0f, 0.8f, 0.0f); // Gold
        resourceColors[ResourceType.MetallicHydrogen] = new Color(0.0f, 0.9f, 0.9f); // Cyan
        resourceColors[ResourceType.Antimatter] = new Color(1.0f, 0.0f, 1.0f); // Magenta
        resourceColors[ResourceType.Helium3] = new Color(0.8f, 1.0f, 0.2f); // Yellow-green
    }

    /// <summary>
    /// Get the icon sprite for a resource type
    /// </summary>
    public Sprite GetResourceIcon(ResourceType type)
    {
        if (resourceIcons.ContainsKey(type))
            return resourceIcons[type];
            
        //Debug.LogWarning($"No icon found for resource type: {type}");
        return null;
    }
    
    /// <summary>
    /// Initialize market prices with starting values
    /// </summary>
    private void InitializeMarketPrices()
    {
        // Basic resources - starting prices
        marketPrices[ResourceType.Ore] = 1;
        marketPrices[ResourceType.Ice] = 1;
        marketPrices[ResourceType.Carbon] = 1;
        marketPrices[ResourceType.Silicon] = 1;
        marketPrices[ResourceType.RareEarths] = 2;
        
        // Advanced resources - starting prices
        marketPrices[ResourceType.Alloys] = 3;
        marketPrices[ResourceType.Fuel] = 3;
        marketPrices[ResourceType.Graphene] = 3;
        marketPrices[ResourceType.Ceramics] = 3;
        marketPrices[ResourceType.Microchips] = 4;
        
        // Ultimate resources - starting prices
        marketPrices[ResourceType.Superconductors] = 8;
        marketPrices[ResourceType.MetallicHydrogen] = 8;
        marketPrices[ResourceType.Antimatter] = 10;
        marketPrices[ResourceType.Helium3] = 8;
    }
    
    /// <summary>
    /// Get the color for a resource type
    /// </summary>
    public Color GetResourceColor(ResourceType type)
    {
        if (resourceColors.ContainsKey(type))
            return resourceColors[type];
            
        return Color.white;
    }
    
    /// <summary>
    /// Get the current market price for a resource (buying price)
    /// </summary>
    public int GetMarketBuyPrice(ResourceType type)
    {
        if (marketPrices.ContainsKey(type))
            return marketPrices[type];
            
        return 0;
    }

    /// <summary>
    /// Get the market sell price for a resource (current market price)
    /// </summary>
    public int GetMarketSellPrice(ResourceType type)
    {
        if (marketPrices.ContainsKey(type))
            return marketPrices[type];

        return 0;
    }

    
    /// <summary>
    /// Buy resources from the Earth market
    /// </summary>
    public bool BuyFromMarket(Player player, ResourceType type, int amount)
    {
        if (!marketPrices.ContainsKey(type))
        {
            Debug.LogWarning($"Resource type {type} not available on the market");
            return false;
        }
        
        // Calculate total cost at current price
        int currentPrice = marketPrices[type];
        int totalCost = currentPrice * amount;
        
        // Check if player can afford it
        if (player.Money < totalCost)
        {
            Debug.LogWarning($"Player {player.PlayerId} cannot afford to buy {amount} {type} (Cost: ${totalCost}, Has: ${player.Money})");
            return false;
        }
        
        // Deduct money
        player.Money -= totalCost;
        
        // Add resources to Earth
        GameObject earth = WorldManager.Instance.GetCelestialBodyByName("Earth");
        if (earth != null)
        {
            GameLogManager logManager = GameLogManager.Instance;
            if (logManager != null)
            {
                logManager.AddLog($"Player {player.PlayerId + 1} bought {amount} {type} for ${totalCost}");
            }

            player.AddResource(earth, type, amount);
            
            // Increase price by 1 for each unit bought
            marketPrices[type] += amount;
            return true;
        }
        else
        {
            Debug.LogError("Earth not found in WorldManager!");
            return false;
        }
    }

    /// <summary>
    /// Sell resources to the Earth market
    /// </summary>
    public bool SellToMarket(Player player, ResourceType type, int amount)
    {
        if (!marketPrices.ContainsKey(type))
        {
            Debug.LogWarning($"Resource type {type} not sellable on the market");
            return false;
        }

        // Get Earth
        GameObject earth = WorldManager.Instance.GetCelestialBodyByName("Earth");
        if (earth == null)
        {
            Debug.LogError("Earth not found in WorldManager!");
            return false;
        }

        // Check if player has enough resources on Earth
        int available = player.GetResourceAmount(earth, type);
        if (available < amount)
        {
            Debug.LogWarning($"Player {player.PlayerId} has only {available} {type} on Earth, tried to sell {amount}");
            return false;
        }

        // Calculate total money received at current sell price
        int sellPrice = marketPrices[type];
        int totalMoney = sellPrice * amount;

        // Remove resources
        player.UseResource(earth, type, amount);

        // Add money
        player.Money += totalMoney;

        // Decrease price by 1 for each unit sold AFTER the sale (minimum price of 1)
        marketPrices[type] = Mathf.Max(1, marketPrices[type] - amount);

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            logManager.AddLog($"Player {player.PlayerId + 1} sold {amount} {type} for ${totalMoney}");
        }

        return true;
    }
    
    /// <summary>
    /// Process resources with a specific processor module
    /// </summary>
    public bool ProcessResources(Player player, GameObject location, Module processor)
    {
        if (processor.Type != ModuleType.Processor)
        {
            Debug.LogWarning($"Module {processor.Name} is not a processor");
            return false;
        }
        
        // Check power requirement
        int availablePower = player.GetAvailablePower(location);
        if (availablePower < processor.PowerRequired)
        {
            Debug.LogWarning($"Not enough power at {location.name} to activate {processor.Name} (Needs {processor.PowerRequired}, has {availablePower})");
            return false;
        }
        
        // Check the processor type and handle input/output resources accordingly
        switch (processor.Name)
        {
            case "Kiln Module":
                return ProcessKiln(player, location);
                
            case "Polymer Factory Module":
                return ProcessPolymerFactory(player, location);
                
            case "Electrolysis Module":
                return ProcessElectrolysis(player, location);
                
            case "Refinery Module":
                return ProcessRefinery(player, location);
                
            case "Smelter Module":
                return ProcessSmelter(player, location);
                
            case "Lithography Module":
                return ProcessLithography(player, location);
                
            case "Vacuum Data Module":
                return ProcessVacuumData(player, location);
                
            case "Quantum Forge Module":
                return ProcessQuantumForge(player, location);
                
            case "Diamond Anvil":
                return ProcessDiamondAnvil(player, location);
                
            case "Super Collider":
                return ProcessSuperCollider(player, location);
                
                
            default:
                Debug.LogWarning($"Unknown processor type: {processor.Name}");
                return false;
        }
    }
    
    // Tier 1 processors
    
    /// <summary>
    /// Process Kiln: 1 Power, 1 Carbon, 1 Silicon -> 1 Ceramic
    /// </summary>
    private bool ProcessKiln(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Carbon) < 1 ||
            player.GetResourceAmount(location, ResourceType.Silicon) < 1)
        {
            Debug.LogWarning($"Not enough resources for Kiln at {location.name}");
            return false;
        }
        
        // Use power (handled by caller)
        
        // Use input resources
        player.UseResource(location, ResourceType.Carbon, 1);
        player.UseResource(location, ResourceType.Silicon, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.Ceramics, 1);
        
        Debug.Log($"Player {player.PlayerId} used Kiln at {location.name}: -1 Carbon, -1 Silicon, +1 Ceramic");
        return true;
    }
    
    /// <summary>
    /// Process Polymer Factory: 1 Power, 1 Ice, 1 Carbon -> 1 Graphene
    /// </summary>
    private bool ProcessPolymerFactory(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Ice) < 1 ||
            player.GetResourceAmount(location, ResourceType.Carbon) < 1)
        {
            Debug.LogWarning($"Not enough resources for Polymer Factory at {location.name}");
            return false;
        }
        
        // Use input resources
        player.UseResource(location, ResourceType.Ice, 1);
        player.UseResource(location, ResourceType.Carbon, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.Graphene, 1);
        
        Debug.Log($"Player {player.PlayerId} used Polymer Factory at {location.name}: -1 Ice, -1 Carbon, +1 Plastic");
        return true;
    }
    
    /// <summary>
    /// Process Electrolysis: 1 Power, 1 Ice -> 1 Fuel
    /// </summary>
    private bool ProcessElectrolysis(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Ice) < 1)
        {
            Debug.LogWarning($"Not enough resources for Electrolysis at {location.name}");
            return false;
        }
        
        // Use input resources
        player.UseResource(location, ResourceType.Ice, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.Fuel, 1);
        
        Debug.Log($"Player {player.PlayerId} used Electrolysis at {location.name}: -1 Ice, +1 Fuel");
        return true;
    }
    
    /// <summary>
    /// Process Refinery: 1 Power, 1 Carbon -> 1 Fuel
    /// </summary>
    private bool ProcessRefinery(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Carbon) < 1)
        {
            Debug.LogWarning($"Not enough resources for Refinery at {location.name}");
            return false;
        }
        
        // Use input resources
        player.UseResource(location, ResourceType.Carbon, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.Fuel, 1);
        
        Debug.Log($"Player {player.PlayerId} used Refinery at {location.name}: -1 Carbon, +1 Fuel");
        return true;
    }
    
    /// <summary>
    /// Process Smelter: 1 Power, 2 Ore, 1 Carbon -> 1 Alloy
    /// </summary>
    private bool ProcessSmelter(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Ore) < 2 ||
            player.GetResourceAmount(location, ResourceType.Carbon) < 1)
        {
            Debug.LogWarning($"Not enough resources for Smelter at {location.name}");
            return false;
        }
        
        // Use input resources
        player.UseResource(location, ResourceType.Ore, 2);
        player.UseResource(location, ResourceType.Carbon, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.Alloys, 1);
        
        Debug.Log($"Player {player.PlayerId} used Smelter at {location.name}: -2 Ore, -1 Carbon, +1 Alloy");
        return true;
    }
    
    /// <summary>
    /// Process Lithography: 1 Power, 1 Silicon, 1 Rare Earths -> 1 Microchip
    /// </summary>
    private bool ProcessLithography(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Silicon) < 1 ||
            player.GetResourceAmount(location, ResourceType.RareEarths) < 1)
        {
            Debug.LogWarning($"Not enough resources for Lithography at {location.name}");
            return false;
        }
        
        // Use input resources
        player.UseResource(location, ResourceType.Silicon, 1);
        player.UseResource(location, ResourceType.RareEarths, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.Microchips, 1);
        
        Debug.Log($"Player {player.PlayerId} used Lithography at {location.name}: -1 Silicon, -1 Rare Earths, +1 Microchip");
        return true;
    }
    
    /// <summary>
    /// Process Vacuum Data: 1 Power -> 1 Science
    /// Must be on a barren planet, ice-shell planet, or in orbit
    /// </summary>
    private bool ProcessVacuumData(Player player, GameObject location)
    {
        // Check if location is valid
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            if (planetBody.Type != PlanetBody.BodyType.Barren && 
                planetBody.Type != PlanetBody.BodyType.IceShell)
            {
                Debug.LogWarning($"Vacuum Data Module can only be used on barren planets, ice-shell planets, or in orbit");
                return false;
            }
        }
        
        // Add Science
        player.ScienceValue += 1;
        
        Debug.Log($"Player {player.PlayerId} used Vacuum Data Module at {location.name}: +1 Science");
        return true;
    }
    
    // Tier 2 processors
    
    /// <summary>
    /// Process Quantum Forge: 2 Power, 2 Rare Earths, 2 Ceramic -> 1 Superconductor
    /// </summary>
    private bool ProcessQuantumForge(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.RareEarths) < 2 ||
            player.GetResourceAmount(location, ResourceType.Ceramics) < 2)
        {
            Debug.LogWarning($"Not enough resources for Quantum Forge at {location.name}");
            return false;
        }
        
        // Check power (would be handled by caller)
        
        // Use input resources
        player.UseResource(location, ResourceType.RareEarths, 2);
        player.UseResource(location, ResourceType.Ceramics, 2);
        
        // Add output resource
        player.AddResource(location, ResourceType.Superconductors, 1);
        
        Debug.Log($"Player {player.PlayerId} used Quantum Forge at {location.name}: -2 Rare Earths, -2 Ceramics, +1 Superconductor");
        return true;
    }
    
    /// <summary>
    /// Process Diamond Anvil: 5 Power, 1 Carbon -> 1 Metallic Hydrogen
    /// </summary>
    private bool ProcessDiamondAnvil(Player player, GameObject location)
    {
        // Check resources
        if (player.GetResourceAmount(location, ResourceType.Carbon) < 1)
        {
            Debug.LogWarning($"Not enough resources for Diamond Anvil at {location.name}");
            return false;
        }
        
        // Use input resources
        player.UseResource(location, ResourceType.Carbon, 1);
        
        // Add output resource
        player.AddResource(location, ResourceType.MetallicHydrogen, 1);
        
        Debug.Log($"Player {player.PlayerId} used Diamond Anvil at {location.name}: -1 Carbon, +1 Metallic Hydrogen");
        return true;
    }
    
    /// <summary>
    /// Process Super Collider: 5 Power -> 1 Antimatter
    /// </summary>
    private bool ProcessSuperCollider(Player player, GameObject location)
    {
        // No input resources needed besides power
        
        // Add output resource
        player.AddResource(location, ResourceType.Antimatter, 1);
        
        Debug.Log($"Player {player.PlayerId} used Super Collider at {location.name}: +1 Antimatter");
        return true;
    }
}