using UnityEngine;

/// <summary>
/// Initializes the TechBuildHandler in the scene
/// </summary>
public class TechBuildHandlerInitializer : MonoBehaviour
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    private static void InitializeTechBuildHandler()
    {
        Debug.Log("[TechBuildHandlerInitializer] RuntimeInitializeOnLoadMethod called");

        // Force creation of TechBuildHandler instance
        var instance = TechBuildHandler.Instance;
        Debug.Log($"[TechBuildHandlerInitializer] TechBuildHandler instance created: {instance != null}");
    }

    private void Start()
    {
        Debug.Log("[TechBuildHandlerInitializer] Start() called");

        // Check if TechBuildHandler already exists
        if (TechBuildHandler.Instance == null)
        {
            Debug.Log("[TechBuildHandlerInitializer] TechBuildHandler.Instance is null, creating new instance");

            // Create a new GameObject for the TechBuildHandler
            GameObject techBuildHandlerObject = new GameObject("TechBuildHandler");
            techBuildHandlerObject.AddComponent<TechBuildHandler>();

            Debug.Log("[TechBuildHandlerInitializer] TechBuildHandler initialized successfully");
        }
        else
        {
            Debug.Log("[TechBuildHandlerInitializer] TechBuildHandler.Instance already exists");
        }
    }
}
