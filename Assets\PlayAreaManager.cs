using System.Collections.Generic;
using UnityEngine;
using TMPro;

/// <summary>
/// Manages all player play areas and interfaces with the game systems
/// </summary>
public class PlayAreaManager : MonoBehaviour
{
    // Singleton pattern
    public static PlayAreaManager Instance { get; private set; }
    
    [Header("Player Play Areas")]
    [SerializeField] private PlayerPlayArea[] playerPlayAreas;
    
    // Cache of celestial bodies
    private Dictionary<string, GameObject> celestialBodies = new Dictionary<string, GameObject>();
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;

        // Cache all celestial bodies in the scene
        CacheCelestialBodies();
    }
    
    private void Start()
    {
        // Initialize player play areas
        InitializePlayAreas();
        
        // Subscribe to player events
        SubscribeToPlayerEvents();
    }
    
    /// <summary>
    /// Cache all celestial bodies in the scene for quick lookup
    /// </summary>
    private void CacheCelestialBodies()
    {
        // Find all celestial bodies in the scene
        PlanetBody[] planetBodies = FindObjectsByType<PlanetBody>(FindObjectsSortMode.None);
        
        foreach (PlanetBody body in planetBodies)
        {
            celestialBodies[body.name] = body.gameObject;
        }
    }
    
    /// <summary>
    /// Initialize all player play areas
    /// </summary>
    private void InitializePlayAreas()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
        {
            Debug.LogError("GameManager not found!");
            return;
        }
        
        int playerCount = gameManager.PlayerCount;
        
        // Make sure we have enough play areas
        if (playerPlayAreas.Length < playerCount)
        {
            Debug.LogWarning($"Not enough player play areas! Have {playerPlayAreas.Length}, need {playerCount}");
        }
        
        // Initialize each player's play area
        for (int i = 0; i < playerPlayAreas.Length; i++)
        {
            if (playerPlayAreas[i] != null)
            {
                // Only activate needed play areas
                playerPlayAreas[i].gameObject.SetActive(i < playerCount);
            }
        }
    }

    /// <summary>
    /// Subscribe to relevant player events
    /// </summary>
    private void SubscribeToPlayerEvents()
    {
        // This would subscribe to events like:
        // - Player acquired a module
        // - Player built a ship
        // - Player moved a ship
        // - Player resources changed
        // - Player acquired a technology
        
        // For example:
        // GameEvents.OnPlayerAcquiredModule.AddListener(OnPlayerAcquiredModule);
        // GameEvents.OnPlayerBuiltShip.AddListener(OnPlayerBuiltShip);
        // GameEvents.OnPlayerAcquiredTechnology.AddListener(OnPlayerAcquiredTechnology);
    }
    
    /// <summary>
    /// Handle a player acquiring a module
    /// </summary>
    public void OnPlayerAcquiredModule(int playerId, Module module, GameObject location)
    {
        if (playerId >= 0 && playerId < playerPlayAreas.Length && playerPlayAreas[playerId] != null)
        {
            playerPlayAreas[playerId].AddModuleCard(location, module);
            Debug.Log($"Player {playerId} acquired module {module.Name} at {location.name}");
        }
    }
    
    /// <summary>
    /// Handle a player building a ship
    /// </summary>
    public void OnPlayerBuiltShip(int playerId, Ship ship, GameObject location)
    {
        if (playerId >= 0 && playerId < playerPlayAreas.Length && playerPlayAreas[playerId] != null)
        {
            playerPlayAreas[playerId].AddShipCard(location, ship);
            Debug.Log($"Player {playerId} built ship {ship.Name} at {location.name}");
        }
    }
    
    /// <summary>
    /// Handle a player acquiring a technology
    /// </summary>
    public void OnPlayerAcquiredTechnology(int playerId, TechnologyCard technology)
    {
        if (playerId >= 0 && playerId < playerPlayAreas.Length && playerPlayAreas[playerId] != null)
        {
            playerPlayAreas[playerId].AddTechnologyCard(technology.Name);
            Debug.Log($"Player {playerId} acquired technology {technology.Name}");
        }
    }
    
    /// <summary>
    /// Handle a player moving a ship
    /// </summary>
    public void OnPlayerMovedShip(int playerId, Ship ship, GameObject fromLocation, GameObject toLocation)
    {
        if (playerId >= 0 && playerId < playerPlayAreas.Length && playerPlayAreas[playerId] != null)
        {
            // Find the ship card in the play area
            PlayerPlayArea playArea = playerPlayAreas[playerId];
            List<GameObject> moduleCards = playArea.GetModuleCardsAtWorld(fromLocation);
            
            foreach (GameObject card in moduleCards)
            {
                ShipCardVisual shipCard = card.GetComponent<ShipCardVisual>();
                if (shipCard != null && shipCard.GetShip() == ship)
                {
                    // Move the card
                    playArea.MoveShipCard(card, fromLocation, toLocation);
                    Debug.Log($"Player {playerId} moved ship {ship.Name} from {fromLocation.name} to {toLocation.name}");
                    break;
                }
            }
        }
    }
    
    /// <summary>
    /// Handle a ship being consumed (for survey probes)
    /// </summary>
    public void OnShipConsumed(int playerId, Ship ship, GameObject location)
    {
        if (playerId >= 0 && playerId < playerPlayAreas.Length && playerPlayAreas[playerId] != null)
        {
            // Find the ship card in the play area
            PlayerPlayArea playArea = playerPlayAreas[playerId];
            List<GameObject> moduleCards = playArea.GetModuleCardsAtWorld(location);
            
            foreach (GameObject card in moduleCards)
            {
                ShipCardVisual shipCard = card.GetComponent<ShipCardVisual>();
                if (shipCard != null && shipCard.GetShip() == ship)
                {
                    // Remove the card
                    playArea.RemoveCard(location, card);
                    Debug.Log($"Player {playerId}'s ship {ship.Name} was consumed at {location.name}");
                    break;
                }
            }
        }
    }
    
    /// <summary>
    /// Handle player resources changing
    /// </summary>
    public void OnPlayerResourcesChanged(int playerId, GameObject location, ResourceType resourceType, int newAmount)
    {
        // Find the corresponding player play area and reposition cards
        PlayerPlayArea playArea = GetPlayerPlayArea(playerId);
        if (playArea != null)
        {
            playArea.RepositionModuleCards(location);
        }

        // Update 3D resource displays
        ResourceDisplayManager resourceDisplayManager = FindFirstObjectByType<ResourceDisplayManager>();
        if (resourceDisplayManager != null)
        {
            Player player = GameManager.Instance?.Players[playerId];
            if (player != null)
            {
                Dictionary<ResourceType, int> resources = player.GetResourcesOnPlanet(location);
                resourceDisplayManager.UpdatePlayerResourceDisplay(playerId, location, resources);
            }
        }
    }
    
    /// <summary>
    /// Get a player's play area
    /// </summary>
    public PlayerPlayArea GetPlayerPlayArea(int playerId)
    {
        if (playerId >= 0 && playerId < playerPlayAreas.Length)
            return playerPlayAreas[playerId];
            
        Debug.LogWarning($"Player play area not found for Player {playerId}");
        return null;
    }
    
    /// <summary>
    /// Refresh all play areas
    /// </summary>
    public void RefreshAllPlayAreas()
    {
        // This would recreate/refresh all cards based on the current game state
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        for (int i = 0; i < gameManager.Players.Count; i++)
        {
            if (i < playerPlayAreas.Length && playerPlayAreas[i] != null)
            {
                RefreshPlayerPlayArea(i);
            }
        }
    }

    /// <summary>
    /// Refresh a specific player's play area
    /// </summary>
    public void RefreshPlayerPlayArea(int playerId)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;

        Player player = gameManager.Players[playerId];
        PlayerPlayArea playArea = playerPlayAreas[playerId];

        if (player == null || playArea == null)
            return;

        // Update player info displays
        if (playArea.moneyText != null)
            playArea.moneyText.text = $"{player.Money}";

        if (playArea.scienceText != null)
        {
            playArea.scienceText.text = $"{player.ScienceValue}";
            playArea.scienceText.ForceMeshUpdate();
        }

        if (playArea.vpText != null)
            playArea.vpText.text = $"{player.VictoryPoints}";

        playArea.UpdateInfoDisplay();
        Canvas.ForceUpdateCanvases();

        // Get all worlds with resources or modules
        List<GameObject> worldsWithResources = player.GetPlanetBodiesWithResources();
        List<GameObject> worldsWithModules = player.GetPlanetBodiesWithModules();

        HashSet<GameObject> allWorlds = new HashSet<GameObject>();
        foreach (GameObject world in worldsWithResources)
            allWorlds.Add(world);
        foreach (GameObject world in worldsWithModules)
            allWorlds.Add(world);

        // Remove ALL module and ship cards from ALL worlds
        foreach (GameObject world in allWorlds)
        {
            List<GameObject> existingCards = new List<GameObject>(playArea.GetModuleCardsAtWorld(world));

            foreach (GameObject card in existingCards)
            {
                bool isModuleOrShipCard = false;

                // Check if it's a ship card
                if (card.name.StartsWith("Ship_"))
                {
                    isModuleOrShipCard = true;
                }
                // Check if it's a module card
                else if (card.name.StartsWith("Module_"))
                {
                    isModuleOrShipCard = true;
                }
                // Backup check using CardData
                else
                {
                    Card3DClickHandler clickHandler = card.GetComponent<Card3DClickHandler>();
                    if (clickHandler != null)
                    {
                        System.Reflection.FieldInfo cardDataField = typeof(Card3DClickHandler).GetField("cardData",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        if (cardDataField != null)
                        {
                            CardData cardData = cardDataField.GetValue(clickHandler) as CardData;
                            if (cardData != null &&
                                (cardData.Type == "Ship" || cardData.Type == "Module" ||
                                 cardData.SubType == "Ship" || cardData.SubType.Contains("Module") ||
                                 cardData.Name.Contains("Rocket") || cardData.Name.Contains("Probe")))
                            {
                                isModuleOrShipCard = true;
                            }
                        }
                    }
                }

                if (isModuleOrShipCard)
                {
                    playArea.RemoveCard(world, card);
                }
            }
        }

        // Add world cards and update resource displays
        foreach (GameObject world in allWorlds)
        {
            GameObject worldCard = playArea.AddWorldCard(world);

            // Update resource displays
            ResourceDisplayManager resourceDisplayManager = FindFirstObjectByType<ResourceDisplayManager>();
            if (resourceDisplayManager != null)
            {
                Dictionary<ResourceType, int> resources = player.GetResourcesOnPlanet(world);
                resourceDisplayManager.UpdatePlayerResourceDisplay(playerId, world, resources);
            }
        }

        // Add all modules
        foreach (GameObject world in allWorlds)
        {
            List<Module> modules = player.GetModulesOnPlanet(world);
            foreach (Module module in modules)
            {
                playArea.AddModuleCard(world, module);
            }
        }

        // Add all ships
        List<Ship> ships = player.GetShips();
        foreach (Ship ship in ships)
        {
            if (ship.CurrentLocation != null)
            {
                playArea.AddShipCard(ship.CurrentLocation, ship);
            }
        }

        // Refresh technology cards
        RefreshPlayerTechnologyCards(playerId);
    }
    
    /// <summary>
    /// Refresh a player's technology cards based on their owned technologies
    /// </summary>
    private void RefreshPlayerTechnologyCards(int playerId)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null || playerId >= gameManager.Players.Count)
            return;
            
        Player player = gameManager.Players[playerId];
        PlayerPlayArea playArea = playerPlayAreas[playerId];
        
        if (player == null || playArea == null)
            return;
            
        // Get all technologies owned by the player
        List<TechnologyCardData> technologies = player.GetTechnologies();
        
        // Add technology cards for each technology
        foreach (TechnologyCardData tech in technologies)
        {
            if (!playArea.HasTechnologyCard(tech.cardName))
            {
                playArea.AddTechnologyCard(tech.cardName);
            }
        }
    }
}